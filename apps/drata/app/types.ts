import type { PageHeaderOverrides } from '@controllers/page-header';
import type { UtilitiesName } from '@controllers/route';

export interface Handle {
    overrides: {
        includeSlotPadding?: boolean;

        pageHeader?: PageHeaderOverrides;
    };
}

export interface Topic {
    id?: string;
    topicPath: string;
    label: string;
    icon?: string;
    metadata?: {
        label: string;
        type: string;
        colorScheme: string;
    };
}

interface Domain {
    label: string;
    hideLabel?: boolean;
    topicsOrder: string[];
    topics: Record<string, Topic>;
    icon?: string;
}

interface TopicsNav {
    id?: string;
    title?: string;
    domainsOrder?: string[];
    domains?: Record<string, Domain>;
}

interface Tab {
    id?: string;
    topicPath: string;
    label: string;
    metadata?: {
        label: string;
        type: string;
        colorScheme: string;
    };
    iconName?: string;
}

interface DomainsNav {
    id: string;
    title: string;
    domainsOrder: string[];
    domains: Record<string, Domain>;
}

interface UtilitiesOverrides {
    utilitiesList: UtilitiesName[];
}

interface LayoutOverrides {
    centered?: boolean;
}

export interface ClientLoaderOptions {
    domainsNav?: DomainsNav;
    pageHeader?: PageHeaderOverrides;
    utilities?: UtilitiesOverrides;
    topicsNav?: TopicsNav | { topicsNav: TopicsNav };
    tabs?: Tab[];
    contentNav?: {
        tabs: Tab[];
    };
    layout?: LayoutOverrides;
    subdomainConfig?: {
        id: string;
        userPart: string;
    };
}

export type ClientLoader = ClientLoaderOptions | null;
