import { sharedConnectionsController } from '@controllers/connections';
import { sharedPoliciesController } from '@controllers/policies';
import { sharedUsersController } from '@controllers/users';
import { action } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Policies' }];

export const clientLoader = action((): ClientLoader => {
    sharedPoliciesController.activePolicies.load();
    sharedPoliciesController.overview.load();
    sharedConnectionsController.allConfiguredConnectionsQuery.load();
    sharedUsersController.usersList.load();

    return {
        pageHeader: sharedPoliciesPageHeaderModel,
        tabs: [
            {
                id: 'governance.policies.active',
                topicPath: 'governance/policies/active',
                label: 'Active',
            },
            {
                id: 'governance.policies.archive',
                topicPath: 'governance/policies/archive',
                label: 'Archive',
            },
        ],
    };
});

const Policies = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Policies" data-id="MviHNu9x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Policies;
