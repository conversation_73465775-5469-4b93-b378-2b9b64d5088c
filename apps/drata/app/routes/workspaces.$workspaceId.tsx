import { MainAppDomainsNavComponent } from '@components/main-app-domains-nav';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { PageAsideUi } from '@ui/page-aside';
import { sharedDomainsNavModel } from '../../../../models/domains-nav/src';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'WorkspaceIdFlat' }];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { workspaceId } = params;

        if (!workspaceId) {
            throw new Error('Workspace ID is required');
        }

        sharedWorkspacesController.setCurrentWorkspaceById(Number(workspaceId));

        return sharedFeatureAccessModel.isNavReadEnabled
            ? {
                  domainsNav: sharedDomainsNavModel.domainsNav,
                  subdomainConfig: {
                      id: 'main',
                      userPart: `/workspaces/${workspaceId}`,
                  },
              }
            : null;
    },
);

const WorkspaceIdFlat = (): React.JSX.Element => {
    return (
        <Stack
            direction="row"
            data-testid="WorkspaceIdFlat"
            data-id="workspaceIdFlat"
            height="100%"
            minHeight="0"
        >
            <MainAppDomainsNavComponent />
            <Outlet />
            <PageAsideUi />
        </Stack>
    );
};

export default WorkspaceIdFlat;
