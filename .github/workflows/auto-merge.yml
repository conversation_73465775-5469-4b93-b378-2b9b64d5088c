name: 'Auto Merge PR'

concurrency:
    group: 'auto-merge-${{ github.ref }}'
    cancel-in-progress: true

on:
    workflow_dispatch:
        inputs:
            branch:
                description: 'Base branch to merge into'
                required: true
                type: choice
                options:
                    - 'main'
    schedule:
        - cron: '0 * * * *' # hourly

jobs:
    auto-merge-pr:
        runs-on:
            [
                runs-on,
                'run-id=${{ github.run_id }}',
                runner=nonprod-small,
                tag=auto-merge-pr,
            ]
        timeout-minutes: 301 # ~(MAX_PRS_PER_RUN * MAX_CHECKS_WAIT / 60) + CHECK_INTERVAL
        cancel-timeout-minutes: 2 # ~CHECK_INTERVAL, let last poll of check and merge PR if possible
        steps:
            - name: 'Auto Merge PR'
              uses: 'drata/utils-actions/auto-merge-pr@2.3.2'
              with:
                  gh-token: ${{ secrets.AUTO_MERGE_PAT }}
                  repo: ${{ github.repository }}
                  base-branch: ${{ github.event.inputs.branch || 'main' }}
