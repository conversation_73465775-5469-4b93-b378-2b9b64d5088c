import { isEmpty } from 'lodash-es';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

export const AccessReviewActionCell = ({
    row: { original },
}: {
    row: {
        original: Pick<AccessReviewPeriodDatatable, 'actions'>;
    };
}): React.JSX.Element => {
    const { items, colorScheme } = original.actions;

    if (isEmpty(items)) {
        return <div />;
    }

    return (
        <SchemaDropdown
            isIconOnly
            size="sm"
            startIconName="HorizontalMenu"
            level="tertiary"
            label={t`Horizontal menu`}
            colorScheme={colorScheme}
            items={items}
            data-testid="AccessReviewActionCell"
            data-id="6GvBWQ_R"
        />
    );
};
