import { Box } from '@cosmos/components/box';
import {
    BulkActions,
    type DatatableProps,
    EmptyStateTable,
    FiltersViewModeProvider,
    GalleryContainer,
    Pagination,
    PinnedFilters,
    type RowData,
    Table,
    TopBar,
    useDatatable,
} from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { dimension3xl } from '@cosmos/constants/tokens';
import { zIndex } from '@cosmos/constants/z-index';
import { useElementHeightAsCSSVar } from '@cosmos/hooks/use-element-height-as-css-var';

export const AppDatatable = <TData extends RowData>({
    'data-id': dataId,
    isFullPageTable = false,
    ...props
}: DatatableProps<TData>): React.JSX.Element => {
    const { table } = useDatatable<TData>({
        'data-id': dataId,
        ...props,
    });

    const { viewMode, hidePagination, showEmptyState, showTopBar } =
        table.getDisplayState();

    const paginationElementRef = useElementHeightAsCSSVar({
        cssVariableName: 'appDatatablePaginationHeight',
    });

    return (
        <FiltersViewModeProvider<TData>
            table={table}
            data-testid="AppDatatable"
            data-id={dataId}
        >
            <Stack
                gap="xl"
                direction="row"
                flexGrow={isFullPageTable ? '1' : undefined}
                height={isFullPageTable ? undefined : '100%'}
                minHeight={isFullPageTable ? undefined : '0'}
                width="100%"
                maxWidth="100%"
                minWidth="0"
                position="relative"
            >
                {/* TODO: Give consumers access to "areFiltersPinnedOpen" */}
                {/* <Stack
                    height="100%"
                    minHeight="0"
                    // TODO: padding should read from the layout padding token
                    pt="3xl"
                    pb="3xl"
                > */}
                <PinnedFilters
                    data-id={`${dataId}-pinned-filters`}
                    table={table}
                />
                {/* </Stack> */}

                <Stack
                    direction="column"
                    width="100%"
                    minWidth="0"
                    position="relative"
                >
                    {showTopBar && (
                        <Box
                            position={isFullPageTable ? 'sticky' : 'static'}
                            backgroundColor="neutralBackgroundSurfaceInitial"
                            top={
                                isFullPageTable
                                    ? 'var(--contentNavigationMenuHeight, 0px)'
                                    : undefined
                            }
                            style={
                                isFullPageTable
                                    ? {
                                          zIndex: zIndex.sticky,
                                          // TODO: padding and margin should read from the layout padding token, keeping them together in `style` for now
                                          paddingTop: dimension3xl,
                                          // NOTE: Negative margin enables "conditional" padding for the sticky container
                                          marginTop: `calc(0px - ${dimension3xl})`,
                                      }
                                    : undefined
                            }
                        >
                            <TopBar table={table} data-id={dataId} />
                        </Box>
                    )}

                    <Box
                        borderPosition="x"
                        borderColor="neutralBorderFaded"
                        borderWidth="borderWidthSm"
                        height="100%"
                        minHeight="0"
                        position={'relative'}
                    >
                        {showEmptyState && <EmptyStateTable table={table} />}
                        {viewMode === 'table' && !showEmptyState && (
                            <Table table={table} />
                        )}
                        {viewMode === 'gallery' && !showEmptyState && (
                            <GalleryContainer table={table} />
                        )}
                        <Stack
                            position="sticky"
                            width="100%"
                            justify="center"
                            left="0"
                            style={{
                                zIndex: zIndex.sticky,
                                bottom: `calc(var(--appDatatablePaginationHeight, 0px) + ${dimension3xl})`,
                            }}
                        >
                            <BulkActions table={table} />
                        </Stack>
                    </Box>

                    {!hidePagination && (
                        <Box
                            ref={paginationElementRef}
                            position={isFullPageTable ? 'sticky' : 'static'}
                            bottom={isFullPageTable ? '0' : undefined}
                            backgroundColor="neutralBackgroundSurfaceInitial"
                            style={
                                isFullPageTable
                                    ? {
                                          zIndex: zIndex.sticky,
                                          // TODO: padding and margin should read from the layout padding token, keeping them together in `style` for now
                                          paddingBottom: dimension3xl,
                                          // NOTE: Negative margin enables "conditional" padding for the sticky container
                                          marginBottom: `calc(0px - ${dimension3xl})`,
                                      }
                                    : undefined
                            }
                        >
                            <Pagination table={table} />
                        </Box>
                    )}
                </Stack>
            </Stack>
        </FiltersViewModeProvider>
    );
};
